package com.gnico.majo.infrastructure.startup

import com.gnico.majo.infrastructure.config.Database
import com.gnico.majo.infrastructure.config.ExternalDatabase
import com.gnico.majo.infrastructure.config.NetworkConfiguration
import com.gnico.majo.infrastructure.afip.ValidationResult
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import org.slf4j.LoggerFactory

/**
 * Maneja la inicialización de la aplicación
 */
class ApplicationStartup : KoinComponent {
    
    private val logger = LoggerFactory.getLogger(ApplicationStartup::class.java)
    private val afipStartupService: AfipStartupService by inject()
    
    /**
     * Inicializa todos los componentes de la aplicación
     */
    fun initialize(): StartupResult {
        logger.info("🚀 Iniciando aplicación...")
        
        val results = mutableMapOf<String, Boolean>()
        
        try {
            // 0. Configurar red y conectividad
            logger.info("🌐 Configurando conectividad de red...")
            NetworkConfiguration.initialize()
            results["network"] = true
            logger.info("✅ Configuración de red completada")

            // 1. Inicializar base de datos principal
            logger.info("📊 Inicializando base de datos principal...")
            Database.init()
            results["database"] = true
            logger.info("✅ Base de datos principal inicializada")
            
            // 2. Inicializar base de datos externa (opcional)
            logger.info("📊 Inicializando base de datos externa...")
            try {
                ExternalDatabase.init()
                results["external_database"] = true
                logger.info("✅ Base de datos externa inicializada")
            } catch (e: Exception) {
                logger.warn("⚠️  Base de datos externa no disponible: ${e.message}")
                results["external_database"] = false
            }
            
            // 3. Validar credenciales AFIP
            logger.info("🔐 Validando credenciales AFIP...")
            val afipResult = afipStartupService.initializeAfipCredentials()
            results["afip_credentials"] = afipResult.isAllValid()
            
            if (afipResult.isAllValid()) {
                logger.info("✅ Credenciales AFIP validadas correctamente")
            } else {
                logger.warn("⚠️  Algunas credenciales AFIP no están disponibles")
            }
            
            // 4. Verificar componentes críticos
            val criticalComponentsOk = results["database"] == true
            
            if (criticalComponentsOk) {
                logger.info("🎉 Aplicación inicializada correctamente")
                logger.info("📋 Resumen de inicialización:")
                results.forEach { (component, status) ->
                    val icon = if (status) "✅" else "❌"
                    logger.info("   $icon $component: ${if (status) "OK" else "FALLO"}")
                }
            } else {
                logger.error("❌ Fallo en componentes críticos durante la inicialización")
            }
            
            return StartupResult(
                success = criticalComponentsOk,
                componentResults = results,
                afipValidationResult = afipResult
            )
            
        } catch (e: Exception) {
            logger.error("💥 Error crítico durante la inicialización: ${e.message}", e)
            return StartupResult(
                success = false,
                componentResults = results,
                afipValidationResult = null,
                error = e.message
            )
        }
    }
    
    /**
     * Realiza limpieza al cerrar la aplicación
     */
    fun shutdown() {
        logger.info("🛑 Cerrando aplicación...")
        
        try {
            // Cerrar conexiones de base de datos
            Database.close()
            ExternalDatabase.close()
            
            logger.info("✅ Aplicación cerrada correctamente")
            
        } catch (e: Exception) {
            logger.error("Error durante el cierre: ${e.message}", e)
        }
    }
    
    /**
     * Verifica el estado de salud de la aplicación
     */
    fun healthCheck(): HealthStatus {
        logger.debug("🔍 Verificando estado de salud de la aplicación")
        
        return try {
            val afipHealth = afipStartupService.checkCredentialsHealth()
            
            HealthStatus(
                isHealthy = afipHealth.isHealthy(),
                afipStatus = afipHealth,
                timestamp = System.currentTimeMillis()
            )
            
        } catch (e: Exception) {
            logger.error("Error en verificación de salud: ${e.message}", e)
            HealthStatus(
                isHealthy = false,
                afipStatus = CredentialsHealthStatus.CRITICAL,
                timestamp = System.currentTimeMillis(),
                error = e.message
            )
        }
    }
}

/**
 * Resultado de la inicialización de la aplicación
 */
data class StartupResult(
    val success: Boolean,
    val componentResults: Map<String, Boolean>,
    val afipValidationResult: ValidationResult?,
    val error: String? = null
) {
    fun getSuccessfulComponents(): List<String> = 
        componentResults.filter { it.value }.keys.toList()
    
    fun getFailedComponents(): List<String> = 
        componentResults.filter { !it.value }.keys.toList()
    
    fun getComponentSuccessRate(): Double = 
        if (componentResults.isNotEmpty()) {
            componentResults.values.count { it }.toDouble() / componentResults.size
        } else {
            0.0
        }
}

/**
 * Estado de salud de la aplicación
 */
data class HealthStatus(
    val isHealthy: Boolean,
    val afipStatus: CredentialsHealthStatus,
    val timestamp: Long,
    val error: String? = null
) {
    fun getStatusDescription(): String = when {
        error != null -> "ERROR: $error"
        !isHealthy -> "UNHEALTHY"
        afipStatus.isCritical() -> "AFIP_CRITICAL"
        afipStatus.isWarning() -> "AFIP_WARNING"
        else -> "HEALTHY"
    }
}
