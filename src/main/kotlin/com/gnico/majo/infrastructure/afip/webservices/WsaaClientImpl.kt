package com.gnico.majo.infrastructure.afip.webservices

import com.gnico.majo.afip.TimeBuilder
import com.gnico.majo.application.domain.model.AfipCredentials
import https.wsaahomo_afip_gov_ar.ws.services.logincms.LoginCMS
import https.wsaahomo_afip_gov_ar.ws.services.logincms.LoginCMSService
import https.wsaahomo_afip_gov_ar.ws.services.logincms.LoginFault_Exception
import jakarta.xml.ws.soap.SOAPFaultException
import org.bouncycastle.cert.jcajce.JcaCertStore
import org.bouncycastle.cms.CMSProcessableByteArray
import org.bouncycastle.cms.CMSSignedDataGenerator
import org.bouncycastle.cms.jcajce.JcaSignerInfoGeneratorBuilder
import org.bouncycastle.jce.provider.BouncyCastleProvider
import org.bouncycastle.operator.jcajce.JcaContentSignerBuilder
import org.bouncycastle.operator.jcajce.JcaDigestCalculatorProviderBuilder
import org.slf4j.LoggerFactory
import java.io.InputStream
import java.security.KeyStore
import java.security.PrivateKey
import java.security.Security
import java.security.cert.X509Certificate
import java.util.*

/**
 * Cliente mejorado para el webservice WSAA de AFIP
 * Maneja la autenticación y obtención de credenciales
 */
class WsaaClientImpl(
    private val configuration: AfipConfiguration
) {
    
    private val logger = LoggerFactory.getLogger(WsaaClientImpl::class.java)
    
    /**
     * Realiza login en WSAA y obtiene credenciales
     */
    fun login(service: String = "wsfe"): AfipCredentials? {
        logger.info("Iniciando autenticación WSAA para servicio: $service")
        
        return try {
            logger.info("Generando TRA...")
            val tra = generateTRA(service)
            logger.info("TRA generado: ${tra}")

            logger.info("Firmando TRA con certificado...")
            val signedCms = signXmlWithCMS(tra)
            logger.info("TRA firmado correctamente")

            logger.info("Enviando solicitud a AFIP...")
            val response = sendLoginRequest(signedCms)


            AfipCredentials(
                token = response.token,
                sign = response.sign,
                cuit = configuration.cuit
            )
        } catch (e: Exception) {
            logger.error("Error en autenticación WSAA: ${e.message}", e)
            null
        }
    }
    
    fun sendLoginRequest(signedCms: String): TokenAndSign {
        val loginService = LoginCMSService()
        val port: LoginCMS = loginService.loginCms
        
        try {
            val response = port.loginCms(signedCms)
            val token = extractValue(response, "token")
            val sign = extractValue(response, "sign")
            
            logger.info("AFIP WSAA authentication successful!")
            return TokenAndSign(token, sign)
            
        } catch (e: LoginFault_Exception) {
            logger.error("AFIP WSAA authentication failed: ${e.message}")
            logger.error("Fault info: ${e.faultInfo}")
            throw AfipAuthenticationException("Error de autenticación AFIP: ${e.message}", e)
            
        } catch (e: SOAPFaultException) {
            val fault = e.fault
            val faultCode = fault?.faultCode
            val faultString = fault?.faultString
            logger.error("AFIP WSAA SOAP Fault detected!")
            logger.error("Fault Code: $faultCode")
            logger.error("Fault String: $faultString")
            throw AfipAuthenticationException("Error SOAP en AFIP: $faultString", e)
            
        } catch (e: Exception) {
            logger.error("Unexpected error during AFIP WSAA authentication: ${e.message}", e)
            throw AfipAuthenticationException("Error inesperado en autenticación AFIP: ${e.message}", e)
        }
    }
    
    fun generateTRA(service: String): String {
        val uniqueId = System.currentTimeMillis() / 1000
        val generationTime = TimeBuilder.getTenMinutesBefore()
        val expirationTime = TimeBuilder.getTenMinutesLater()
        
        return """<?xml version="1.0" encoding="UTF-8"?>
            <loginTicketRequest version="1.0">
                <header>
                    <uniqueId>$uniqueId</uniqueId>
                    <generationTime>$generationTime</generationTime>
                    <expirationTime>$expirationTime</expirationTime>
                </header>
                <service>$service</service>
            </loginTicketRequest>""".trimIndent()
    }
    
    fun signXmlWithCMS(xml: String): String {
        Security.addProvider(BouncyCastleProvider())
        
        // Cargar el keystore PKCS12
        val p12Stream: InputStream = Thread.currentThread().contextClassLoader
            .getResourceAsStream(configuration.certificatePath)
            ?: throw IllegalArgumentException("Certificado P12 no encontrado: ${configuration.certificatePath}")
        
        val keystore = KeyStore.getInstance("PKCS12")
        p12Stream.use {
            keystore.load(it, configuration.certificatePassword.toCharArray())
        }
        
        // Obtener clave privada y certificado
        val privateKey = keystore.getKey(configuration.certificateAlias, configuration.certificatePassword.toCharArray()) as PrivateKey
        val cert = keystore.getCertificate(configuration.certificateAlias) as X509Certificate
        val certChain = keystore.getCertificateChain(configuration.certificateAlias).map { it as X509Certificate }
        
        // Preparar CMS signed data
        val certList = mutableListOf<X509Certificate>(cert)
        certList.addAll(certChain)
        val certStore = JcaCertStore(certList)
        
        val signedDataGenerator = CMSSignedDataGenerator()
        val contentSigner = JcaContentSignerBuilder("SHA256withRSA").build(privateKey)
        val signerInfoGenerator = JcaSignerInfoGeneratorBuilder(
            JcaDigestCalculatorProviderBuilder().build()
        ).build(contentSigner, cert)
        
        signedDataGenerator.addSignerInfoGenerator(signerInfoGenerator)
        signedDataGenerator.addCertificates(certStore)
        
        // Crear CMS signed data con el contenido XML
        val cmsProcessable = CMSProcessableByteArray(xml.toByteArray(Charsets.UTF_8))
        val signedData = signedDataGenerator.generate(cmsProcessable, true)
        
        return Base64.getEncoder().encodeToString(signedData.encoded)
    }
    
    fun extractValue(xml: String, tagName: String): String {
        val startTag = "<$tagName>"
        val endTag = "</$tagName>"
        val startIndex = xml.indexOf(startTag)
        val endIndex = xml.indexOf(endTag)
        
        if (startIndex == -1 || endIndex == -1) {
            throw IllegalArgumentException("Tag '$tagName' no encontrado en la respuesta XML")
        }
        
        return xml.substring(startIndex + startTag.length, endIndex)
    }
}

/**
 * Configuración para AFIP
 */
data class AfipConfiguration(
    val cuit: Long,
    val certificatePath: String,
    val certificatePassword: String,
    val certificateAlias: String = "1",
    val isProduction: Boolean = false
)

/**
 * Datos de token y sign de AFIP
 */
data class TokenAndSign(val token: String, val sign: String)

/**
 * Excepción específica para errores de autenticación AFIP
 */
class AfipAuthenticationException(message: String, cause: Throwable? = null) : Exception(message, cause)
