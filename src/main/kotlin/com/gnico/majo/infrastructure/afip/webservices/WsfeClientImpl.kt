package com.gnico.majo.infrastructure.afip.webservices

import com.gnico.majo.afip.TimeBuilder
import com.gnico.majo.application.domain.model.*
import fev1.dif.afip.gov.ar.*
import jakarta.xml.ws.BindingProvider
import org.slf4j.LoggerFactory
import java.net.SocketException

/**
 * Cliente mejorado para el webservice WSFE de AFIP
 * Implementa la lógica completa de solicitud de CAE
 * Incluye configuración robusta para evitar errores de conexión
 */
class WsfeClientImpl {

    private val logger = LoggerFactory.getLogger(WsfeClientImpl::class.java)

    companion object {
        private const val CONNECTION_TIMEOUT = 30000 // 30 segundos
        private const val REQUEST_TIMEOUT = 60000 // 60 segundos
        private const val MAX_RETRIES = 3
        private const val RETRY_DELAY_MS = 2000L // 2 segundos
    }
    
    /**
     * Solicita un CAE para una venta específica
     */
    fun requestCAE(request: AfipCAERequest): WsfeResponse {
        logger.info("Solicitando CAE para venta ${request.sale.numeroVenta}")

        return executeWithRetry {
            attemptRequestCAE(request)
        }
    }

    private fun attemptRequestCAE(request: AfipCAERequest): WsfeResponse {
        val service = Service()
        val port = service.serviceSoap

        // Configurar cliente con timeouts
        configureWebServiceClient(port, "wsfe")

        try {
            // 1. Crear autenticación
            val auth = createAuthRequest(request.credentials)

            // 2. Crear solicitud de factura
            val caeRequest = createCAERequest(request)

            // 3. Llamar al servicio
            val response = port.fecaeSolicitar(auth, caeRequest)

            // 4. Procesar respuesta
            return processResponse(response, request.sale.numeroVenta)

        } catch (e: SocketException) {
            logger.error("Socket error during CAE request for sale ${request.sale.numeroVenta}: ${e.message}")
            throw AfipWebserviceException("Error de conexión con AFIP (Connection reset): ${e.message}", e)

        } catch (ex: Exception) {
            logger.error("Error al solicitar CAE para venta ${request.sale.numeroVenta}: ${ex.message}", ex)
            throw AfipWebserviceException("Error al comunicarse con AFIP: ${ex.message}", ex)
        }
    }
    
    /**
     * Obtiene el último número de comprobante para un punto de venta y tipo
     */
    fun getLastInvoiceNumber(
        credentials: AfipCredentials,
        puntoVenta: Int,
        tipoComprobante: TipoComprobanteAfip
    ): Long {
        logger.info("Obteniendo último número de comprobante para PV: $puntoVenta, Tipo: ${tipoComprobante.codigo}")

        return executeWithRetry {
            attemptGetLastInvoiceNumber(credentials, puntoVenta, tipoComprobante)
        }
    }

    private fun attemptGetLastInvoiceNumber(
        credentials: AfipCredentials,
        puntoVenta: Int,
        tipoComprobante: TipoComprobanteAfip
    ): Long {
        val service = Service()
        val port = service.serviceSoap

        // Configurar cliente con timeouts
        configureWebServiceClient(port, "wsfe")

        try {
            val auth = createAuthRequest(credentials)
            val response = port.feCompUltimoAutorizado(auth, puntoVenta, tipoComprobante.codigo)

            return response?.cbteNro?.toLong() ?: 0L

        } catch (e: SocketException) {
            logger.error("Socket error getting last invoice number: ${e.message}")
            throw AfipWebserviceException("Error de conexión con AFIP: ${e.message}", e)

        } catch (ex: Exception) {
            logger.error("Error al obtener último número de comprobante: ${ex.message}", ex)
            return 0L
        }
    }
    
    private fun createAuthRequest(credentials: AfipCredentials): FEAuthRequest {
        return FEAuthRequest().apply {
            token = credentials.token
            sign = credentials.sign
            cuit = credentials.cuit
        }
    }
    
    private fun createCAERequest(request: AfipCAERequest): FECAERequest {
        val invoiceData = request.toAfipInvoiceData()
        
        // Obtener próximo número de comprobante
        val lastNumber = getLastInvoiceNumber(
            request.credentials,
            request.puntoVenta,
            request.tipoComprobante
        )
        val nextNumber = lastNumber + 1
        
        // Crear detalle de la factura
        val invoice = FECAEDetRequest().apply {
            concepto = invoiceData.concepto
            docTipo = invoiceData.docTipo
            docNro = invoiceData.docNro
            cbteDesde = nextNumber
            cbteHasta = nextNumber
            cbteFch = TimeBuilder.getCurrentDate()
            impTotal = invoiceData.impTotal.toDouble()
            impTotConc = invoiceData.impTotConc.toDouble()
            impNeto = invoiceData.impNeto.toDouble()
            impIVA = invoiceData.impIva.toDouble()
            impTrib = invoiceData.impTrib.toDouble()
            monId = invoiceData.monId
            monCotiz = invoiceData.monCotiz.toDouble()
        }
        
        // Agregar detalles de IVA si existen
        if (invoiceData.ivaDetails.isNotEmpty()) {
            val ivaArray = ArrayOfAlicIva().apply {
                invoiceData.ivaDetails.forEach { ivaDetail ->
                    alicIva.add(AlicIva().apply {
                        id = ivaDetail.id
                        baseImp = ivaDetail.baseImponible.toDouble()
                        importe = ivaDetail.importe.toDouble()
                    })
                }
            }
            invoice.iva = ivaArray
        }
        
        // Crear la solicitud principal
        return FECAERequest().apply {
            feCabReq = FECAECabRequest().apply {
                cantReg = 1
                ptoVta = request.puntoVenta
                cbteTipo = request.tipoComprobante.codigo
            }
            feDetReq = ArrayOfFECAEDetRequest().apply {
                fecaeDetRequest.add(invoice)
            }
        }
    }
    
    private fun processResponse(response: FECAEResponse?, saleNumber: String): WsfeResponse {
        if (response == null) {
            throw AfipWebserviceException("No se recibió respuesta del servicio AFIP")
        }
        
        // Verificar errores globales
        if (response.errors?.getErr()?.isNotEmpty() == true) {
            val errors = response.errors.getErr().map { "${it.code}: ${it.msg}" }
            logger.error("Errores en respuesta AFIP para venta $saleNumber: $errors")
            return WsfeResponse(
                cae = "ERROR",
                caeFchVto = "",
                resultado = "R",
                numeroComprobante = 0,
                observaciones = errors
            )
        }
        
        // Procesar respuesta del detalle
        val detResp = response.feDetResp?.getFECAEDetResponse()?.firstOrNull()
        if (detResp == null) {
            throw AfipWebserviceException("No se recibió detalle de respuesta de AFIP")
        }
        
        val observaciones = mutableListOf<String>()
        
        // Agregar eventos como observaciones
        response.events?.getEvt()?.forEach { evt ->
            observaciones.add("${evt.code}: ${evt.msg}")
        }
        
        // Agregar observaciones del detalle
        detResp.observaciones?.getObs()?.forEach { obs ->
            observaciones.add("${obs.code}: ${obs.msg}")
        }
        
        logger.info("CAE procesado para venta $saleNumber: ${detResp.resultado}, CAE: ${detResp.cae}")
        
        return WsfeResponse(
            cae = detResp.cae ?: "ERROR",
            caeFchVto = detResp.caeFchVto ?: "",
            resultado = detResp.resultado ?: "R",
            numeroComprobante = detResp.cbteDesde ?: 0,
            observaciones = observaciones
        )
    }

    /**
     * Configura el cliente del webservice con timeouts y headers apropiados
     */
    private fun configureWebServiceClient(port: Any, serviceName: String) {
        val bindingProvider = port as BindingProvider
        val requestContext = bindingProvider.requestContext

        // Configurar URL del endpoint según el entorno
        val endpointUrl = if (serviceName == "wsfe") {
            "https://wswhomo.afip.gov.ar/wsfev1/service.asmx" // Homologación por defecto
        } else {
            requestContext[BindingProvider.ENDPOINT_ADDRESS_PROPERTY] as String
        }

        // Configurar timeouts
        requestContext[BindingProvider.ENDPOINT_ADDRESS_PROPERTY] = endpointUrl
        requestContext["com.sun.xml.ws.connect.timeout"] = CONNECTION_TIMEOUT
        requestContext["com.sun.xml.ws.request.timeout"] = REQUEST_TIMEOUT
        requestContext["com.sun.xml.internal.ws.connect.timeout"] = CONNECTION_TIMEOUT
        requestContext["com.sun.xml.internal.ws.request.timeout"] = REQUEST_TIMEOUT

        // Headers HTTP adicionales
        val httpHeaders = mutableMapOf<String, List<String>>()
        httpHeaders["User-Agent"] = listOf("AFIP-WS-Client/1.0")
        httpHeaders["Accept"] = listOf("text/xml, application/soap+xml")
        httpHeaders["Connection"] = listOf("keep-alive")
        requestContext["com.sun.xml.ws.api.message.MessageContextProperties.HTTP_REQUEST_HEADERS"] = httpHeaders

        logger.debug("Cliente webservice $serviceName configurado con timeouts: connect=$CONNECTION_TIMEOUT, request=$REQUEST_TIMEOUT")
    }

    /**
     * Ejecuta una operación con lógica de retry
     */
    private fun <T> executeWithRetry(operation: () -> T): T {
        var lastException: Exception? = null

        repeat(MAX_RETRIES) { attempt ->
            try {
                return operation()
            } catch (e: AfipWebserviceException) {
                // Verificar si es un error de conexión que vale la pena reintentar
                if (e.cause is SocketException || e.message?.contains("Connection reset") == true) {
                    lastException = e
                    logger.warn("Intento ${attempt + 1}/$MAX_RETRIES falló con error de conexión: ${e.message}")

                    if (attempt < MAX_RETRIES - 1) {
                        logger.info("Reintentando en ${RETRY_DELAY_MS}ms...")
                        Thread.sleep(RETRY_DELAY_MS)
                        continue
                    }
                }
                // Para otros errores de AFIP, no reintentar
                throw e
            } catch (e: Exception) {
                lastException = e
                logger.warn("Intento ${attempt + 1}/$MAX_RETRIES falló: ${e.message}")

                if (attempt < MAX_RETRIES - 1) {
                    logger.info("Reintentando en ${RETRY_DELAY_MS}ms...")
                    Thread.sleep(RETRY_DELAY_MS)
                }
            }
        }

        throw AfipWebserviceException(
            "Falló después de $MAX_RETRIES intentos: ${lastException?.message}",
            lastException
        )
    }
}

/**
 * Excepción específica para errores de webservices de AFIP
 */
class AfipWebserviceException(message: String, cause: Throwable? = null) : Exception(message, cause)
