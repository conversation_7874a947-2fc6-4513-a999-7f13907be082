package com.gnico.majo.infrastructure.routes

import com.gnico.majo.adapter.controller.rest.SaleController
import com.gnico.majo.adapter.controller.dto.ErrorResponse
import com.gnico.majo.adapter.controller.dto.SaleRequest
import com.gnico.majo.adapter.controller.dto.SaleCreatedResponse
import kotlinx.serialization.Serializable
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.route
import io.ktor.server.routing.routing

fun Application.configureSaleRoutes(saleController: SaleController) {
    routing {
        route("/api/sales") {

            post {
                try {
                    println("🔍 POST /api/sales - Recibiendo petición")
                    val request = call.receive<SaleRequest>()
                    println("🔍 Request recibido: vendedor=${request.vendedor}, items=${request.items.size}")
                    val saleId = saleController.createSale(request)
                    println("✅ Venta creada con ID: ${saleId.value}")
                    call.respond(HttpStatusCode.Created, SaleCreatedResponse(saleId.value))
                } catch (e: IllegalArgumentException) {
                    println("❌ Error de argumento: ${e.message}")
                    e.printStackTrace()
                    call.respond(HttpStatusCode.BadRequest, ErrorResponse(e.message ?: "Invalid request"))
                } catch (e: Exception) {
                    println("❌ Error interno: ${e.message}")
                    e.printStackTrace()
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            get("/scale/{codigo}") {
                try {
                    val codigo = call.parameters["codigo"]
                    if (codigo.isNullOrBlank()) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("Código es requerido"))
                        return@get
                    }

                    val saleDetails = saleController.getSaleDetails(codigo)
                    if (saleDetails != null) {
                        call.respond(HttpStatusCode.OK, saleDetails)
                    } else {
                        call.respond(HttpStatusCode.NotFound, ErrorResponse("Venta no encontrada"))
                    }
                } catch (e: IllegalArgumentException) {
                    call.respond(HttpStatusCode.BadRequest, ErrorResponse(e.message ?: "Invalid request"))
                } catch (e: Exception) {
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // Nuevas rutas para consultar ventas

            // GET /api/sales/{ventaId} - Buscar venta por ID
            get("/{ventaId}") {
                try {
                    val ventaIdParam = call.parameters["ventaId"]
                    if (ventaIdParam.isNullOrBlank()) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("ventaId es requerido"))
                        return@get
                    }

                    val ventaId = ventaIdParam.toIntOrNull()
                    if (ventaId == null) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("ventaId debe ser un número válido"))
                        return@get
                    }

                    println("🔍 GET /api/sales/$ventaId")
                    val sale = saleController.getSaleById(ventaId)
                    if (sale != null) {
                        call.respond(HttpStatusCode.OK, sale)
                    } else {
                        call.respond(HttpStatusCode.NotFound, ErrorResponse("Venta no encontrada"))
                    }

                } catch (e: Exception) {
                    println("❌ Error al buscar venta: ${e.message}")
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // GET /api/sales/numero/{numeroVenta} - Buscar venta por número
            get("/numero/{numeroVenta}") {
                try {
                    val numeroVenta = call.parameters["numeroVenta"]
                    if (numeroVenta.isNullOrBlank()) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("numeroVenta es requerido"))
                        return@get
                    }

                    println("🔍 GET /api/sales/numero/$numeroVenta")
                    val sale = saleController.getSaleByNumero(numeroVenta)
                    if (sale != null) {
                        call.respond(HttpStatusCode.OK, sale)
                    } else {
                        call.respond(HttpStatusCode.NotFound, ErrorResponse("Venta no encontrada"))
                    }

                } catch (e: Exception) {
                    println("❌ Error al buscar venta: ${e.message}")
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // POST /api/sales/buscar-por-fecha - Buscar ventas por rango de fechas
            post("/buscar-por-fecha") {
                try {
                    println("🔍 POST /api/sales/buscar-por-fecha")
                    val request = call.receive<BuscarVentasPorFechaRequest>()

                    if (request.fechaDesde.isBlank() || request.fechaHasta.isBlank()) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("fechaDesde y fechaHasta son requeridos"))
                        return@post
                    }

                    println("🔍 Búsqueda: desde=${request.fechaDesde}, hasta=${request.fechaHasta}")
                    val sales = saleController.getSalesByDateRange(request.fechaDesde, request.fechaHasta)
                    println("✅ Encontradas ${sales.size} ventas")
                    call.respond(HttpStatusCode.OK, sales)

                } catch (e: IllegalArgumentException) {
                    println("❌ Error de argumento: ${e.message}")
                    call.respond(HttpStatusCode.BadRequest, ErrorResponse(e.message ?: "Invalid request"))
                } catch (e: Exception) {
                    println("❌ Error al buscar ventas: ${e.message}")
                    e.printStackTrace()
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // GET /api/sales/usuario/{username} - Buscar ventas por usuario
            get("/usuario/{username}") {
                try {
                    val username = call.parameters["username"]
                    if (username.isNullOrBlank()) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("username es requerido"))
                        return@get
                    }

                    println("🔍 GET /api/sales/usuario/$username")
                    val sales = saleController.getSalesByUsuario(username)
                    println("✅ Encontradas ${sales.size} ventas para usuario $username")
                    call.respond(HttpStatusCode.OK, sales)

                } catch (e: Exception) {
                    println("❌ Error al buscar ventas: ${e.message}")
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }
        }
    }
}

@Serializable
data class BuscarVentasPorFechaRequest(
    val fechaDesde: String,  // Formato: yyyy-MM-dd HH:mm:ss
    val fechaHasta: String   // Formato: yyyy-MM-dd HH:mm:ss
)
