package com.gnico.majo.infrastructure.config

import com.gnico.majo.utils.Env
import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import java.sql.Connection
import java.sql.SQLException

object ExternalDatabase {

    private var dataSource: HikariDataSource? = null
    private var isAvailable: Boolean = false

    fun init() {
        try {
            val config = HikariConfig().apply {
                jdbcUrl = Env.get("EXTERNAL_POSTGRES_DB_URL")
                username = Env.get("EXTERNAL_POSTGRES_USER")
                password = Env.get("EXTERNAL_POSTGRES_PASSWORD")
                maximumPoolSize = 5
                isAutoCommit = true // Para consultas simples de solo lectura
                connectionTimeout = 10000
                idleTimeout = 600000
                maxLifetime = 1800000
            }
            dataSource = HikariDataSource(config)

            // Test the connection to make sure it's working
            dataSource?.connection?.use {
                // Connection test successful
                isAvailable = true
                println("✓ Conexión a base de datos externa establecida correctamente")
            }
        } catch (e: Exception) {
            isAvailable = false
            dataSource = null
            println("⚠ No se pudo conectar a la base de datos externa: ${e.message}")
            println("  La aplicación continuará funcionando con funcionalidad limitada")
        }
    }

    fun isAvailable(): Boolean = isAvailable

    fun getConnection(): Connection? {
        return try {
            if (!isAvailable || dataSource == null) {
                return null
            }
            dataSource?.connection
        } catch (e: SQLException) {
            println("⚠ Error al obtener conexión de la base de datos externa: ${e.message}")
            null
        }
    }

    fun close() {
        try {
            dataSource?.close()
            isAvailable = false
        } catch (e: Exception) {
            println("Error al cerrar la conexión de la base de datos externa: ${e.message}")
        }
    }
}
