package com.gnico.majo.afip.webservices.wsaa

import com.gnico.majo.afip.TimeBuilder
import https.wsaahomo_afip_gov_ar.ws.services.logincms.LoginCMS
import https.wsaahomo_afip_gov_ar.ws.services.logincms.LoginCMSService
import https.wsaahomo_afip_gov_ar.ws.services.logincms.LoginFault_Exception
import jakarta.xml.ws.soap.SOAPFaultException
import org.bouncycastle.cert.jcajce.JcaCertStore
import org.bouncycastle.cms.CMSProcessableByteArray
import org.bouncycastle.cms.CMSSignedDataGenerator
import org.bouncycastle.cms.jcajce.JcaSignerInfoGeneratorBuilder
import org.bouncycastle.jce.provider.BouncyCastleProvider
import org.bouncycastle.operator.jcajce.JcaContentSignerBuilder
import org.bouncycastle.operator.jcajce.JcaDigestCalculatorProviderBuilder
import org.slf4j.LoggerFactory
import java.io.InputStream
import java.security.KeyStore
import java.security.PrivateKey
import java.security.Security
import java.security.cert.X509Certificate
import java.util.*


fun main() {
    try {
        val client = WsaaClient()
        println("Generando TRA...")
        val tra = client.generateTRA("wsfe")
        println("TRA generado: ${tra.take(100)}...")
        
        println("Firmando TRA con certificado...")
        val signedCms = client.signXmlWithCMS(
            xml = tra,
            p12ResourcePath = "afip/Certificado.p12",
            p12Password = "gnico",
            alias = "1"
        )
        println("TRA firmado correctamente")
        
        println("Enviando solicitud a AFIP...")
        val tokensign = client.login("wsfe")
        
        if (tokensign?.token.isNullOrEmpty() || tokensign?.sign.isNullOrEmpty()) {
            println("ERROR: No se obtuvieron credenciales válidas")
        } else {
            println("Sign: " + tokensign?.sign)
            println("Token: " + tokensign?.token)
        }
    } catch (e: Exception) {
        println("ERROR CRÍTICO: ${e.message}")
        e.printStackTrace()
    }
}


data class TokenAndSign(val token: String, val sign: String)


class WsaaClient {

    private val logger = LoggerFactory.getLogger(WsaaClient::class.java)

    fun login(service: String = "wsfe") : TokenAndSign? {
        val tra = generateTRA(service)
        val signedCms = signXmlWithCMS(
            xml = tra,
            p12ResourcePath = "afip/Certificado.p12",
            p12Password = "gnico",
            alias = "1"
        )
        return sendLoginRequest(signedCms)

    }

    private fun sendLoginRequest(signedCms: String) : TokenAndSign {
        val loginService = LoginCMSService()
        val port : LoginCMS = loginService.loginCms
        try {
            val response = port.loginCms(signedCms)
            val token = extractValue(response, "token")
            val sign = extractValue(response, "sign")
            logger.info("AFIP WSAA authentication successful!")
            logger.info("Token: $token")
            logger.info("Sign: $sign")
            return TokenAndSign(token, sign)
        } catch (e : LoginFault_Exception) {
            logger.error("AFIP WSAA authentication failed: ${e.message}")
            logger.error("Fault info: ${e.faultInfo}")
        } catch (e: SOAPFaultException) {
            val fault = e.fault
            val faultCode = fault?.faultCode
            val faultString = fault?.faultString
            logger.error("AFIP WSAA SOAP Fault detected!")
            logger.error("Fault code   : $faultCode")
            logger.error("Fault string : $faultString")
        } catch (e: Exception) {
            logger.error("Unexpected error during WSAA authentication", e)
            e.printStackTrace()
        }
        return TokenAndSign("", "")
    }

    private fun extractValue(xmlResponse: String, tag: String): String {
        val startTag = "<$tag>"
        val endTag = "</$tag>"
        val startIndex = xmlResponse.indexOf(startTag) + startTag.length
        val endIndex = xmlResponse.indexOf(endTag)
        return xmlResponse.substring(startIndex, endIndex)
    }

    fun generateTRA(service : String): String {
        val generationTime = TimeBuilder.getTenMinutesBefore();
        val expirationTime = TimeBuilder.getTenMinutesLater();
        val uniqueId = System.currentTimeMillis() / 1000
        return """
            <?xml version="1.0" encoding="UTF8"?>
            <loginTicketRequest version="1.0">
                <header>
                    <uniqueId>$uniqueId</uniqueId>
                    <generationTime>$generationTime</generationTime>
                    <expirationTime>$expirationTime</expirationTime>
                </header>
                <service>$service</service>
            </loginTicketRequest>
        """.trimIndent()
    }

    fun signXmlWithCMS(xml: String, p12ResourcePath: String, p12Password: String, alias: String = "1"): String {
        Security.addProvider(BouncyCastleProvider())

        // Load the PKCS12 keystore
        val p12Stream: InputStream = Thread.currentThread().contextClassLoader
            .getResourceAsStream(p12ResourcePath)
            ?: throw IllegalArgumentException("P12 resource not found: $p12ResourcePath")

        val keystore = KeyStore.getInstance("PKCS12")
        p12Stream.use {
            keystore.load(it, p12Password.toCharArray())
        }

        // Retrieve private key and certificate chain
        val privateKey = keystore.getKey(alias, p12Password.toCharArray()) as PrivateKey
        val cert = keystore.getCertificate(alias) as X509Certificate
        val certChain = keystore.getCertificateChain(alias).map { it as X509Certificate }

        // Prepare the CMS signed data
        val certList = mutableListOf<X509Certificate>(cert) // Include the signer's certificate
        certList.addAll(certChain) // Include the certificate chain
        val certStore = JcaCertStore(certList)

        // Create the CMS signed data generator
        val signedDataGenerator = CMSSignedDataGenerator()
        val sha256Signer = JcaContentSignerBuilder("SHA256withRSA").setProvider("BC").build(privateKey)
        val signerInfoGenerator = JcaSignerInfoGeneratorBuilder(
            JcaDigestCalculatorProviderBuilder().setProvider("BC").build()
        ).build(sha256Signer, cert)

        // Add signer info and certificates
        signedDataGenerator.addSignerInfoGenerator(signerInfoGenerator)
        signedDataGenerator.addCertificates(certStore)

        // Create CMS signed data with the XML content
        val cmsProcessable = CMSProcessableByteArray(xml.toByteArray(Charsets.UTF_8))
        val signedData = signedDataGenerator.generate(cmsProcessable, true) // true to include content

        //val cmsBytes = signedData.encoded
        return Base64.getEncoder().encodeToString(signedData.encoded)
    }
}

