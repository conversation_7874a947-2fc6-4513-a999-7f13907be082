# Rutas de Consulta de Ventas - Implementación Completa

## ✅ Rutas Implementadas

### 1. **Consulta por ID**
```http
GET /api/sales/{ventaId}
```
- **Descripción**: Busca una venta específica por su ID
- **Parámetros**: `ventaId` (int) - ID de la venta
- **Respuesta**: Objeto `SaleResponse` completo con ítems
- **Ejemplo**: `GET /api/sales/456`

### 2. **Consulta por Número de Venta**
```http
GET /api/sales/numero/{numeroVenta}
```
- **Descripción**: Busca una venta por su número único
- **Parámetros**: `numeroVenta` (string) - Número de venta (ej: V-12345678)
- **Respuesta**: Objeto `SaleResponse` completo con ítems
- **Ejemplo**: `GET /api/sales/numero/V-12345678`

### 3. **Búsqueda por Rango de Fechas**
```http
POST /api/sales/buscar-por-fecha
```
- **Descripción**: Busca ventas en un rango de fechas específico
- **Body**: `BuscarVentasPorFechaRequest`
- **Respuesta**: Array de objetos `SaleResponse`
- **Ejemplo**:
```json
{
  "fechaDesde": "2024-01-01 00:00:00",
  "fechaHasta": "2024-01-31 23:59:59"
}
```

### 4. **Consulta por Usuario**
```http
GET /api/sales/usuario/{username}
```
- **Descripción**: Busca todas las ventas realizadas por un usuario
- **Parámetros**: `username` (string) - Username del vendedor
- **Respuesta**: Array de objetos `SaleResponse`
- **Ejemplo**: `GET /api/sales/usuario/vendedor1`

### 5. **Crear Nueva Venta**
```http
POST /api/sales
```
- **Descripción**: Crea una nueva venta (funcionalidad existente)
- **Body**: `SaleRequest`
- **Respuesta**: `SaleCreatedResponse` con ID de venta

### 6. **Detalles Externos (Balanza)**
```http
GET /api/sales/scale/{codigo}
```
- **Descripción**: Obtiene detalles de venta desde sistema externo
- **Parámetros**: `codigo` (string) - Código de 8 dígitos
- **Respuesta**: `SaleDetailResponse`

## 📋 Estructura de Respuesta

### SaleResponse
```json
{
  "id": 456,
  "numeroVenta": "V-12345678",
  "fechaVenta": "15/01/2024 14:30",
  "clienteId": 123,
  "clienteNombre": "Cliente Ejemplo",
  "usuarioUsername": "vendedor1",
  "usuarioNombre": "Vendedor 1",
  "montoTotal": "1,250.00",
  "medioPago": "EFECTIVO",
  "comprobanteEmitido": true,
  "codigoTicketBalanza": "TKT001",
  "idTicketBalanza": "ID001",
  "items": [
    {
      "productoCodigo": "1001",
      "cantidad": 2.0,
      "precioUnitario": "625.00",
      "subtotal": "1,250.00",
      "tipoIvaId": 5
    }
  ]
}
```

### SaleItemResponse
```json
{
  "productoCodigo": "1001",
  "cantidad": 2.0,
  "precioUnitario": "625.00",
  "subtotal": "1,250.00",
  "tipoIvaId": 5
}
```

## 🔧 Implementación Técnica

### Controlador
- **Archivo**: `SaleController.kt`
- **Métodos agregados**:
  - `getSaleById(ventaId: Int): SaleResponse?`
  - `getSaleByNumero(numeroVenta: String): SaleResponse?`
  - `getSalesByDateRange(fechaDesde: String, fechaHasta: String): List<SaleResponse>`
  - `getSalesByUsuario(username: String): List<SaleResponse>`

### Servicio
- **Archivo**: `SaleService.kt` (interface extendida)
- **Métodos agregados**:
  - `findSaleById(ventaId: Id): Sale?`
  - `findSaleByNumero(numeroVenta: String): Sale?`
  - `findSalesByDateRange(fechaDesde: String, fechaHasta: String): List<Sale>`
  - `findSalesByUsuario(username: String): List<Sale>`

### Repositorio
- **Archivo**: `SaleRepositoryPort.kt` (interface extendida)
- **Métodos agregados**:
  - `findSaleByNumeroVenta(numeroVenta: String): Sale?`
  - `findSalesByDateRange(startDate: LocalDateTime, endDate: LocalDateTime): List<Sale>`
  - `findSalesByUsuario(username: String): List<Sale>`
  - `findSalesByCliente(clienteId: Id): List<Sale>`

### DTOs
- **Archivo**: `SaleDto.kt`
- **DTOs agregados**:
  - `SaleResponse` - Respuesta completa de venta
  - `SaleItemResponse` - Respuesta de ítem de venta
  - `SaleCreatedResponse` - Respuesta de creación (renombrado)

### Rutas
- **Archivo**: `SaleRoutes.kt`
- **Rutas agregadas**: 4 nuevas rutas de consulta

## 🎯 Casos de Uso

### 1. Dashboard de Ventas
```kotlin
// Obtener ventas del día actual
val ventasHoy = saleController.getSalesByDateRange(
    fechaDesde = "2024-01-15 00:00:00",
    fechaHasta = "2024-01-15 23:59:59"
)

// Calcular totales
val totalVentas = ventasHoy.size
val montoTotal = ventasHoy.sumOf { it.montoTotal.replace(",", "").toDouble() }
```

### 2. Reporte por Vendedor
```kotlin
// Obtener ventas de un vendedor específico
val ventasVendedor = saleController.getSalesByUsuario("vendedor1")

// Análisis de performance
val ventasConComprobante = ventasVendedor.count { it.comprobanteEmitido }
val porcentajeFacturacion = (ventasConComprobante * 100.0) / ventasVendedor.size
```

### 3. Búsqueda de Venta Específica
```kotlin
// Por número de venta
val venta = saleController.getSaleByNumero("V-12345678")

// Por ID
val ventaPorId = saleController.getSaleById(456)
```

### 4. Análisis de Período
```kotlin
// Ventas del mes
val ventasMes = saleController.getSalesByDateRange(
    fechaDesde = "2024-01-01 00:00:00",
    fechaHasta = "2024-01-31 23:59:59"
)

// Agrupar por día
val ventasPorDia = ventasMes.groupBy { 
    it.fechaVenta.substring(0, 10) // Extraer fecha
}
```

## 🌐 Ejemplos con cURL

### Consultar venta por ID
```bash
curl http://localhost:8080/api/sales/456
```

### Consultar venta por número
```bash
curl http://localhost:8080/api/sales/numero/V-12345678
```

### Buscar ventas por fecha
```bash
curl -X POST http://localhost:8080/api/sales/buscar-por-fecha \
  -H "Content-Type: application/json" \
  -d '{
    "fechaDesde": "2024-01-01 00:00:00",
    "fechaHasta": "2024-01-31 23:59:59"
  }'
```

### Consultar ventas por usuario
```bash
curl http://localhost:8080/api/sales/usuario/vendedor1
```

## ✅ Estado de Implementación

- ✅ **Rutas implementadas**: 6 endpoints
- ✅ **Controlador extendido**: 4 nuevos métodos
- ✅ **Servicio extendido**: 4 nuevos métodos
- ✅ **DTOs creados**: 3 nuevos DTOs
- ✅ **Documentación completa**: Endpoints y ejemplos
- ✅ **Compilación exitosa**: Sin errores ni advertencias
- ✅ **Validaciones**: Parámetros y manejo de errores

El sistema ahora permite consultar ventas realizadas de forma completa y eficiente, complementando perfectamente la funcionalidad de comprobantes desfasados e impresión.
